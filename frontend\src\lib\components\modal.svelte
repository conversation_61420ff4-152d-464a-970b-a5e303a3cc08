<script lang="ts">
  import type { Snippet } from "svelte";

  /**
   * Universal Modal Component
   *
   * A reusable modal component with header, body, and footer sections.
   * The header includes a title and close button.
   * The footer includes cancel and submit buttons.
   */

  // Props
  interface Props {
    children: Snippet;

    /**
     * Controls whether the modal is visible
     */
    show: boolean;

    /**
     * The title displayed in the modal header
     */
    title: string;

    /**
     * Function called when the modal is closed
     */
    onClose: () => void;

    /**
     * Function called when the submit button is clicked
     */
    onSubmit?: () => void;

    /**
     * Text for the submit button
     * @default "Submit"
     */
    submitText?: string;

    /**
     * Text for the cancel button
     * @default "Cancel"
     */
    cancelText?: string;

    /**
     * Whether the submit button is disabled
     * @default false
     */
    submitDisabled?: boolean;

    /**
     * Whether the cancel button is disabled
     * @default false
     */
    cancelDisabled?: boolean;

    /**
     * Whether the submit button is in loading state
     * @default false
     */
    isSubmitting?: boolean;

    /**
     * Size of the modal
     * @default "md"
     */
    size?: "sm" | "md" | "lg" | "xl";

    /**
     * Whether to center the modal vertically
     * @default true
     */
    centered?: boolean;

    /**
     * Whether to show the footer
     * @default true
     */
    showFooter?: boolean;

    /**
     * Whether to show the close button in header
     * @default true
     */
    showCloseButton?: boolean;

    /**
     * Whether to show the submit button
     * @default true
     */
    showSubmitButton?: boolean;
  }

  const {
    children,
    show = false,
    title = "",
    onClose,
    onSubmit,
    submitText = "Submit",
    cancelText = "Cancel",
    submitDisabled = false,
    cancelDisabled = false,
    isSubmitting = false,
    size = "md",
    centered = true,
    showFooter = true,
    showCloseButton = true,
    showSubmitButton = true,
  }: Props = $props();

  // Handle close button click
  function handleClose() {
    onClose();
  }

  // Handle submit button click
  function handleSubmit() {
    if (onSubmit) {
      onSubmit();
    }
  }

  // Determine modal dialog class based on size and centered props
  const modalDialogClass = $derived(
    `modal-dialog modal-${size} ${centered ? "modal-dialog-centered" : ""}`,
  );

  // Disable/enable page scrolling when modal is shown/hidden
  $effect(() => {
    if (show) {
      // Disable page scrolling using both style and class approaches
      document.body.style.overflow = "hidden";
      document.documentElement.style.overflow = "hidden";
      // document.body.classList.add("modal-open");
    } else {
      // Re-enable page scrolling
      document.body.style.overflow = "";
      document.documentElement.style.overflow = "";
      document.body.classList.remove("modal-open");
    }

    // Cleanup function to restore scrolling when component is destroyed
    return () => {
      document.body.style.overflow = "";
      document.documentElement.style.overflow = "";
      document.body.classList.remove("modal-open");
    };
  });
</script>

{#if show}
  <div
    class="modal fade show"
    style="display: block;"
    tabindex="-1"
    aria-modal="true"
    role="dialog"
  >
    <!-- Backdrop -->
    <div class="modal-backdrop fade show"></div>

    <div class={modalDialogClass}>
      <div class="modal-content">
        <!-- Header -->
        <div class="modal-header">
          <h5 class="modal-title">{title}</h5>
          {#if showCloseButton}
            <button type="button" class="btn-close" aria-label="Close" onclick={handleClose}
            ></button>
          {/if}
        </div>

        <!-- Body -->
        <div class="modal-body">
          {@render children()}
        </div>

        <!-- Footer -->
        {#if showFooter}
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              onclick={handleClose}
              disabled={cancelDisabled}
            >
              {cancelText}
            </button>
            {#if showSubmitButton}
              <button
                type="button"
                class="btn btn-primary"
                onclick={handleSubmit}
                disabled={submitDisabled || isSubmitting}
              >
                {isSubmitting ? `${submitText}...` : submitText}
              </button>
            {/if}
          </div>
        {/if}
      </div>
    </div>
  </div>
{/if}

<style>
  /* Custom styling for the modal */

  .modal-header {
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1rem;
  }

  .modal-title {
    margin-bottom: 0;
    line-height: 1.5;
    font-weight: 500;
  }

  .modal-body {
    padding: 1rem;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
  }

  .modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 0.75rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
  }

  /* Ensure the modal appears above other content and fits viewport height */
  .modal-dialog {
    z-index: 1050;
    max-height: 100vh;
    margin: 0.5rem auto;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .modal-content {
    max-height: calc(100vh - 1rem);
    display: flex;
    flex-direction: column;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .modal-backdrop {
    z-index: 1040;
    background-color: #000;
    opacity: 0.5;
  }

  /* Global styles to prevent scrolling when modal is open */
  :global(body.modal-open) {
    overflow: hidden !important;
  }

  :global(html.modal-open) {
    overflow: hidden !important;
  }
</style>
